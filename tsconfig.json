{"extends": "./node_modules/astro/tsconfigs/base.json", "compilerOptions": {"jsx": "react-jsx", "jsxImportSource": "react", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["astro/client"], "strictNullChecks": true, "allowJs": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "noEmit": true, "target": "ES2022", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "skipLibCheck": true}, "include": ["src/**/*"], "exclude": ["node_modules"]}